<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV - <PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            color: #000;
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 1px solid #000;
            padding-bottom: 15px;
        }

        .profile-img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 10px;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .job-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 0;
        }

        .section {
            margin-bottom: 20px;
        }

        h2 {
            font-size: 16px;
            margin-bottom: 10px;
            font-weight: bold;
            text-transform: uppercase;
            border-bottom: 1px solid #ccc;
            padding-bottom: 2px;
        }

        .contact-info p {
            margin: 3px 0;
            font-size: 14px;
        }

        .experience-item {
            margin-bottom: 15px;
        }

        .experience-item h3 {
            font-size: 14px;
            margin-bottom: 2px;
            font-weight: bold;
        }

        .date {
            font-weight: bold;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        ul {
            margin-left: 15px;
            margin-top: 5px;
        }

        li {
            margin-bottom: 2px;
            font-size: 13px;
        }

        .skills,
        .languages,
        .interests {
            font-size: 14px;
        }

        .skill-item,
        .language-item,
        .interest-item {
            display: inline;
            margin-right: 15px;
        }

        .skill-item:after,
        .language-item:after,
        .interest-item:after {
            content: " • ";
        }

        .skill-item:last-child:after,
        .language-item:last-child:after,
        .interest-item:last-child:after {
            content: "";
        }
    </style>
</head>

<body>
    <div class="header">
        <img src="image-removebg-preview.png" alt="Abir Ben Mahmoud" class="profile-img">
        <h1>Abir Ben Mahmoud</h1>
        <p class="job-title">Vendeuse Expérimentée</p>
    </div>

    <div class="section">
        <h2>Contact</h2>
        <div class="contact-info">
            <p><strong>Téléphone:</strong> 23757188</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Adresse:</strong> Sidi Salah, route Tunis, Sfax</p>
        </div>
    </div>

    <div class="section">
        <h2>Profil</h2>
        <p>Je suis passionnée par le service clientèle avec une expérience solide dans la vente. Je souhaite utiliser
            mes compétences en conseil clientèle, gestion des stocks et animation commerciale pour contribuer au succès
            de votre entreprise.</p>
    </div>

    <div class="section">
        <h2>Expérience Professionnelle</h2>

        <div class="experience-item">
            <h3>Vendeuse - Pâtisserie Maison Fondon (spécialité fondant)</h3>
            <div class="date">2024 - 2025 (8 mois)</div>
            <ul>
                <li>Vente de pâtisseries et produits de boulangerie</li>
                <li>Accueil et service clientèle</li>
                <li>Gestion des commandes</li>
                <li>Présentation et mise en valeur des produits</li>
            </ul>
        </div>

        <div class="experience-item">
            <h3>Vendeuse - Boutique de Produits Cosmétiques</h3>
            <div class="date">2024</div>
            <ul>
                <li>Conseil et vente de produits cosmétiques</li>
                <li>Accueil et orientation des clients</li>
                <li>Gestion des stocks et présentation des produits</li>
                <li>Service clientèle personnalisé</li>
            </ul>
        </div>

        <div class="experience-item">
            <h3>Vendeuse - Parapharmacie</h3>
            <div class="date">2024 (2 mois)</div>
            <ul>
                <li>Vente de produits parapharmaceutiques</li>
                <li>Conseil et information des clients</li>
                <li>Gestion de la caisse</li>
                <li>Rangement et organisation des produits</li>
            </ul>
        </div>

        <div class="experience-item">
            <h3>Vendeuse - Lida Chaussures</h3>
            <div class="date">2022 - 2024</div>
            <ul>
                <li>Accueil et conseil des clients avec des recommandations personnalisées</li>
                <li>Gestion des opérations de caisse, y compris encaissement et retours</li>
                <li>Mise en place de présentations visuelles et gestion des stocks</li>
                <li>Formation des nouveaux employés sur les techniques de vente</li>
            </ul>
        </div>

        <div class="experience-item">
            <h3>Vendeuse - Maxi</h3>
            <div class="date">2020 - 2021</div>
            <ul>
                <li>Accueil et conseil clientèle personnalisé</li>
                <li>Gestion de la caisse et traitement des retours</li>
                <li>Mise en place de présentations visuelles</li>
                <li>Réassortiment et gestion des stocks</li>
                <li>Formation des nouveaux employés</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>Formation</h2>
        <div class="experience-item">
            <h3>Niveau Baccalauréat</h3>
            <div class="date">2021</div>
            <p>Majida Boulila, Sfax</p>
        </div>
    </div>

    <div class="section">
        <h2>Compétences</h2>
        <div class="skills">
            <div class="skill-item">Service Clientèle</div>
            <div class="skill-item">Techniques de Vente</div>
            <div class="skill-item">Gestion des Stocks</div>
            <div class="skill-item">Flexibilité et Adaptabilité</div>
        </div>
    </div>

    <div class="section">
        <h2>Langues</h2>
        <div class="languages">
            <div class="language-item">Arabe - Langue maternelle</div>
            <div class="language-item">Français - Niveau intermédiaire</div>
            <div class="language-item">Anglais - Niveau intermédiaire</div>
        </div>
    </div>

    <div class="section">
        <h2>Centres d'Intérêt</h2>
        <div class="interests">
            <div class="interest-item">Mode et tendances actuelles</div>
            <div class="interest-item">Développement personnel</div>
            <div class="interest-item">Techniques de vente</div>
            <div class="interest-item">Activités de bénévolat communautaire</div>
        </div>
    </div>
</body>

</html>