<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV - <PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.5;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4a90e2;
            padding-bottom: 20px;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .profile-img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin-bottom: 15px;
            border: 3px solid #4a90e2;
        }

        h1 {
            font-size: 28px;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .job-title {
            font-size: 16px;
            color: #4a90e2;
            font-weight: bold;
        }

        .section {
            margin-bottom: 25px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        h2 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #4a90e2;
            padding-bottom: 5px;
        }

        .contact-info p {
            margin: 8px 0;
        }

        .contact-info strong {
            color: #4a90e2;
        }

        .experience-item {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #4a90e2;
        }

        .experience-item h3 {
            font-size: 16px;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .date {
            font-weight: bold;
            color: #4a90e2;
            margin-bottom: 10px;
            font-size: 14px;
        }

        ul {
            margin-left: 20px;
        }

        li {
            margin-bottom: 5px;
        }

        .skills,
        .languages,
        .interests {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .skill-item,
        .language-item,
        .interest-item {
            background: #4a90e2;
            color: white;
            padding: 8px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 500;
        }

        .language-item {
            background: #27ae60;
        }

        .interest-item {
            background: #e74c3c;
        }
    </style>
</head>

<body>
    <div class="header">
        <img src="YOUR_IMAGE_PATH_HERE" alt="Abir Ben Mahmoud" class="profile-img">
        <h1>Abir Ben Mahmoud</h1>
        <p class="job-title">Vendeuse Expérimentée</p>
    </div>

    <div class="section">
        <h2>Contact</h2>
        <div class="contact-info">
            <p><strong>Téléphone:</strong> 23757188</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Adresse:</strong> Sidi Salah, route Tunis, Sfax</p>
        </div>
    </div>

    <div class="section">
        <h2>Profil</h2>
        <p>Je suis passionnée par le service client avec une expérience importante dans la vente. Je souhaite utiliser
            mes compétences en conseil client, gestion des stocks et animation commerciale pour contribuer au succès de
            votre entreprise.</p>
    </div>

    <div class="section">
        <h2>Expérience Professionnelle</h2>

        <div class="experience-item">
            <h3>Vendeuse - Pâtisserie Maison Fondon (spécialité fondant)</h3>
            <div class="date">2024 - 2025 (8 mois)</div>
            <ul>
                <li>Vente de pâtisseries et produits de boulangerie</li>
                <li>Accueil et service client</li>
                <li>Gestion des commandes</li>
                <li>Présentation et mise en valeur des produits</li>
            </ul>
        </div>

        <div class="experience-item">
            <h3>Vendeuse - Boutique de Produits Cosmétiques</h3>
            <div class="date">2024 - 2025</div>
            <ul>
                <li>Conseil et vente de produits cosmétiques</li>
                <li>Accueil et orientation des clients</li>
                <li>Gestion des stocks et présentation des produits</li>
                <li>Service client personnalisé</li>
            </ul>
        </div>

        <div class="experience-item">
            <h3>Vendeuse - Parapharmacie</h3>
            <div class="date">2024 (2 mois)</div>
            <ul>
                <li>Vente de produits parapharmaceutiques</li>
                <li>Conseil et information des clients</li>
                <li>Gestion de la caisse</li>
                <li>Rangement et organisation des produits</li>
            </ul>
        </div>

        <div class="experience-item">
            <h3>Vendeuse - Lida Chaussures</h3>
            <div class="date">2022 - 2024</div>
            <ul>
                <li>Accueil et conseil des clients avec des recommandations personnalisées</li>
                <li>Gestion des opérations de caisse, y compris encaissement et retours</li>
                <li>Mise en place de présentations visuelles et gestion des stocks</li>
                <li>Formation des nouveaux employés sur les techniques de vente</li>
            </ul>
        </div>

        <div class="experience-item">
            <h3>Vendeuse - Maxi</h3>
            <div class="date">2020 - 2021</div>
            <ul>
                <li>Accueil et conseil client personnalisé</li>
                <li>Gestion de la caisse et traitement des retours</li>
                <li>Mise en place de présentations visuelles</li>
                <li>Réassortiment et gestion des stocks</li>
                <li>Formation des nouveaux employés</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>Formation</h2>
        <div class="experience-item">
            <h3>Niveau Baccalauréat</h3>
            <div class="date">2021</div>
            <p>Majida Boulila, Sfax</p>
        </div>
    </div>

    <div class="section">
        <h2>Compétences</h2>
        <div class="skills">
            <div class="skill-item">Service Client</div>
            <div class="skill-item">Techniques de Vente</div>
            <div class="skill-item">Gestion des Stocks</div>
            <div class="skill-item">Flexibilité et Adaptabilité</div>
        </div>
    </div>

    <div class="section">
        <h2>Langues</h2>
        <div class="languages">
            <div class="language-item">Arabe - Langue maternelle</div>
            <div class="language-item">Français - Niveau intermédiaire</div>
            <div class="language-item">Anglais - Niveau intermédiaire</div>
        </div>
    </div>

    <div class="section">
        <h2>Centres d'Intérêt</h2>
        <div class="interests">
            <div class="interest-item">Mode et tendances actuelles</div>
            <div class="interest-item">Développement personnel</div>
            <div class="interest-item">Techniques de vente</div>
            <div class="interest-item">Activités de bénévolat communautaire</div>
        </div>
    </div>
</body>

</html>